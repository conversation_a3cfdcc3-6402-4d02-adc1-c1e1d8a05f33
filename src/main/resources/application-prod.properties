spring.application.name=new-cognition-spring
server.port=443
server.ssl.key-store=/usr/local/ssl/api_myhispreadnlp_com.jks
server.ssl.key-store-password=GI34rDJUzsUoAQpf
server.ssl.key-store-type=JKS
server.ssl.enabled=true
#server.http2.enabled=true
server.max-http-request-header-size=8KB
server.servlet.encoding.charset=UTF-8
server.servlet.encoding.force=true
server.servlet.encoding.enabled=true
server.error.whitelabel.enabled=false
spring.data.redis.url=redis://Hispread0719.@192.168.0.32:6379
spring.data.redis.jedis.pool.max-active=100
spring.data.redis.jedis.pool.max-wait=-1
spring.data.redis.jedis.pool.max-idle=100
spring.data.redis.jedis.pool.min-idle=10
spring.data.redis.timeout=5000


spring.kafka.bootstrap-servers=192.168.0.118:9092
spring.kafka.producer.retries=3
spring.kafka.producer.compression-type=lz4
spring.kafka.producer.key-serializer=org.apache.kafka.common.serialization.StringSerializer
spring.kafka.producer.value-serializer=org.apache.kafka.common.serialization.StringSerializer
spring.kafka.producer.acks=1
spring.kafka.producer.batch-size=16384
spring.kafka.producer.buffer-memory=33554432
spring.kafka.producer.properties.max.request.size = 10485760
spring.kafka.producer.properties.sasl.mechanism=PLAIN
spring.kafka.producer.properties.security.protocol=SASL_PLAINTEXT
spring.kafka.producer.properties.sasl.jaas.config=org.apache.kafka.common.security.scram.ScramLoginModule required username="ckafka-mm785ne7#cognition" password="Hispread0719.";


spring.kafka.consumer.auto-commit-interval=1S
spring.kafka.consumer.auto-offset-reset=earliest
spring.kafka.consumer.enable-auto-commit=true
spring.kafka.consumer.key-deserializer=org.apache.kafka.common.serialization.StringDeserializer
spring.kafka.consumer.value-deserializer=org.apache.kafka.common.serialization.StringDeserializer
spring.kafka.consumer.properties.sasl.mechanism=PLAIN
spring.kafka.consumer.properties.security.protocol=SASL_PLAINTEXT
spring.kafka.consumer.properties.sasl.jaas.config=org.apache.kafka.common.security.scram.ScramLoginModule required username="ckafka-mm785ne7#cognition" password="Hispread0719.";

spring.kafka.listener.concurrency=3
spring.kafka.listener.ack-mode=RECORD
spring.kafka.listener.missing-topics-fatal=false
