package com.myhispreadnlp.api.newcognitionspring.channel.provider.openai;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.myhispreadnlp.api.newcognitionspring.cache.CognitionKeysCache;
import com.myhispreadnlp.api.newcognitionspring.channel.enums.ChannelType;
import com.myhispreadnlp.api.newcognitionspring.channel.provider.LlmRemoteService;
import com.myhispreadnlp.api.newcognitionspring.channel.provider.openai.downgrade.ModelDowngradeUtils;
import com.myhispreadnlp.api.newcognitionspring.common.exception.CognitionWebException;
import com.myhispreadnlp.api.newcognitionspring.common.utils.JsonUtils;
import com.myhispreadnlp.api.newcognitionspring.domain.context.ChatContext;
import com.myhispreadnlp.api.newcognitionspring.domain.context.usage.ChatUsage;
import com.myhispreadnlp.api.newcognitionspring.domain.response.openai.ChatCompletion;
import com.myhispreadnlp.api.newcognitionspring.domain.response.openai.ChatCompletionChunk;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpHeaders;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Instant;
import java.util.HashMap;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;

@Slf4j
@Service
@RequiredArgsConstructor
@SuppressWarnings({"DuplicatedCode", "unused", "LoggingSimilarMessage"})
public class GeminiOpenAIRemoteService extends LlmRemoteService {

    @Qualifier("httpWebClient")
    private final WebClient httpWebClient;

    @Qualifier("sseWebClient")
    private final WebClient sseWebClient;

    private final ObjectMapper objectMapper;

    private final CognitionKeysCache cognitionKeysCache;

    public Flux<ServerSentEvent<String>> streamOpenAiCompletion(HashMap<String, Object> body, ChatContext chatContext) {
        var isFirst = new AtomicBoolean(true);
        return Mono.just(body)
                .doOnNext(b -> {
                    chatContext.setChannelType(ChannelType.GOOGLE);
                    cognitionKeysCache.setupKey(chatContext, ChannelType.GOOGLE);
                    log.info("开始Gemini流式请求 | 用户ID: {} | 模型: {} | 密钥: {}",
                            chatContext.getChatUserInfo().getCharactersId(), chatContext.apiModelName(), chatContext.getCurrentApiConfig().getKey());
                    // 设置 Google 格式的模型名称
                    b.put("model", "google/" + chatContext.apiModelName());
                })
                .flatMapMany(key -> sseWebClient.post()
                        .uri(buildGeminiUri(chatContext))
                        .header(HttpHeaders.AUTHORIZATION, "Bearer " + chatContext.getCurrentApiConfig().getKey())
                        .bodyValue(body)
                        .retrieve()
                        .bodyToFlux(String.class)
                        .map(processStream(chatContext, isFirst))
                        .map(response -> (ServerSentEvent.<String>builder().data(" " + response).build()))
                        .doOnComplete(() -> {
                            if (chatContext.getChatUsage().needCounterBySelf()) {
                                log.warn("触发手动计费 | 原因: stream未返回usage内容 | 用户ID: {} | 模型: {} | 密钥: {}",
                                        chatContext.getChatUserInfo().getCharactersId(), chatContext.apiModelName(),
                                        chatContext.getCurrentApiConfig().getKey());
                                chatContext.counterBySelf();
                            }
                        })
                ).doOnError(error -> {
                    loggingError(error, chatContext, ChannelType.GOOGLE);
                    ModelDowngradeUtils.downgradeModel(chatContext);
                });
    }

    private static Function<String, String> processStream(ChatContext chatContext, AtomicBoolean isFirst) {
        Long userId = chatContext.getChatUserInfo().getCharactersId();
        String modelName = chatContext.apiModelName();

        return message -> {
            if (Objects.equals(message.trim(), "[DONE]")) {
                log.debug("Gemini流式请求完成 | 状态: DONE | 用户ID: {} | 模型: {}", userId, modelName);
                return message;
            }

            if (isFirst.getAndSet(false)) {
                chatContext.getChatRequestStatistic().setPreparationTime(Instant.now().toEpochMilli());
                log.debug("收到首个数据块 | 用户ID: {} | 模型: {}", userId, modelName);
            }
            try {
                var chatCompletionChunk = JsonUtils.parseObject(message, ChatCompletionChunk.class);

                // Log detailed response at DEBUG level to avoid excessive logging
                log.debug("接收Gemini数据块 | 用户ID: {} | 模型: {} | 消息内容: {}",
                        userId, modelName, message);

                chatContext.getChatRequestStatistic().getStreamResponse().append(chatCompletionChunk.allContent());

                if (StringUtils.hasLength(chatContext.getChatModelInfo().getBlackModelName())) {
                    chatCompletionChunk.setModel(chatContext.getChatModelInfo().getSystemModelName());
                }

                if (chatCompletionChunk.getUsage() != null) {
                    chatContext.setChatUsage(ChatUsage.create(chatCompletionChunk));
                    log.info("更新使用情况 | 请求类型: 流式 | 用户ID: {} | 模型: {} | 使用量: {}", userId, modelName, JsonUtils.toJSONString(chatCompletionChunk.getUsage()));
                }

                log.debug("数据块解析完成 | 用户ID: {} | 模型: {} | 响应内容: {}",
                        userId, modelName, JsonUtils.toJSONString(chatCompletionChunk));
                return JsonUtils.toJSONString(chatCompletionChunk);
            } catch (Exception e) {
                log.error("数据块解析错误 | 用户ID: {} | 模型: {} | 错误信息: {}",
                        userId, modelName, e.getMessage());
                throw new CognitionWebException("Error occurred with Gemini OpenAI API stream parse to chatCompletionChunk: " + e.getMessage());
            }
        };
    }

    public Mono<Object> nonStreamOpenAiCompletion(HashMap<String, Object> body, ChatContext chatContext) {
        return Mono.just(body)
                .doOnNext(b -> {
                    chatContext.setChannelType(ChannelType.GOOGLE);
                    cognitionKeysCache.setupKey(chatContext, ChannelType.GOOGLE);
                    log.info("开始Gemini非流式请求 | 用户ID: {} | 模型: {} | 密钥: {}",
                            chatContext.getChatUserInfo().getCharactersId(), chatContext.apiModelName(), chatContext.getCurrentApiConfig().getKey());
                    // 设置 Google 格式的模型名称
                    b.put("model", "google/" + chatContext.apiModelName());
                })
                .flatMap(key -> httpWebClient.post()
                        .uri(buildGeminiUri(chatContext))
                        .header(HttpHeaders.AUTHORIZATION, "Bearer " + chatContext.getCurrentApiConfig().getKey())
                        .bodyValue(body)
                        .retrieve()
                        .bodyToMono(String.class)
                        .map(processNonStream(chatContext))
                ).doOnError(error -> {
                    loggingError(error, chatContext, ChannelType.GOOGLE);
                    ModelDowngradeUtils.downgradeModel(chatContext);
                });
    }

    private Function<String, Object> processNonStream(ChatContext chatContext) {
        Long userId = chatContext.getChatUserInfo().getCharactersId();
        String modelName = chatContext.apiModelName();

        return message -> {
            try {
                // Log detailed response at DEBUG level to avoid excessive logging
                log.debug("接收Gemini非流式响应 | 用户ID: {} | 模型: {} | 消息内容: {}",
                        userId, modelName, message);
                var chatCompletion = JsonUtils.parseObject(message, ChatCompletion.class);
                if (StringUtils.hasLength(chatContext.getChatModelInfo().getBlackModelName())) {
                    chatCompletion.setModel(chatContext.getChatModelInfo().getSystemModelName());
                }

                if (chatCompletion.getUsage() != null) {
                    chatContext.setChatUsage(ChatUsage.create(chatCompletion, false));
                    log.info("更新使用情况 | 请求类型: 非流式 | 用户ID: {} | 模型: {} | 使用量: {}", userId, modelName, JsonUtils.toJSONString(chatCompletion.getUsage()));
                }
                return chatCompletion;
            } catch (Exception e) {
                log.error("响应解析错误 | 用户ID: {} | 模型: {} | 错误信息: {}", userId, modelName, e.getMessage());
                throw new CognitionWebException("Error occurred with Gemini OpenAI API stream parse to chatCompletion: " + e.getMessage());
            }
        };
    }

    /**
     * 构建 Gemini OpenAI 兼容 API 的 URI
     * 格式: https://${LOCATION}-aiplatform.googleapis.com/v1beta1/projects/${PROJECT_ID}/locations/${LOCATION}/endpoints/openapi/chat/completions
     *
     * https://aiplatform.googleapis.com/v1/projects/dorth81302/locations/global/publishers/
     * 配置说明:
     * - domain: https://${LOCATION}-aiplatform.googleapis.com
     * - secret: ${PROJECT_ID}
     * - region: ${LOCATION}
     */
    private String buildGeminiUri(ChatContext chatContext) {
        String domain = chatContext.getCurrentApiConfig().getDomain();
        // 如果域名已经包含完整路径，直接使用
        if (domain.contains("/chat/completions")) {
            return domain;
        }
        // 否则构建标准的 Gemini OpenAI 兼容端点
        String projectId = chatContext.getCurrentApiConfig().getSecret();
        String location = chatContext.getCurrentApiConfig().getRegion();

        if (projectId == null || location == null) {
            log.warn("Gemini配置不完整 | projectId: {} | location: {} | 使用默认路径", projectId, location);
            return domain + "/v1beta1/endpoints/openapi/chat/completions";
        }

        return domain + "/v1beta1/projects/" + projectId +
               "/locations/" + location +
               "/endpoints/openapi/chat/completions";
    }
}
