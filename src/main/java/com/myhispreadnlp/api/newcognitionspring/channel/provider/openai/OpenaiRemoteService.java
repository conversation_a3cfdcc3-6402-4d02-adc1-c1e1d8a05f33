package com.myhispreadnlp.api.newcognitionspring.channel.provider.openai;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.myhispreadnlp.api.newcognitionspring.cache.CognitionKeysCache;
import com.myhispreadnlp.api.newcognitionspring.channel.enums.ChannelType;
import com.myhispreadnlp.api.newcognitionspring.channel.provider.LlmRemoteService;
import com.myhispreadnlp.api.newcognitionspring.channel.provider.openai.downgrade.ModelDowngradeUtils;
import com.myhispreadnlp.api.newcognitionspring.common.exception.CognitionWebException;
import com.myhispreadnlp.api.newcognitionspring.common.utils.JsonUtils;
import com.myhispreadnlp.api.newcognitionspring.domain.context.ChatContext;
import com.myhispreadnlp.api.newcognitionspring.domain.context.usage.ChatUsage;
import com.myhispreadnlp.api.newcognitionspring.domain.response.openai.ChatCompletion;
import com.myhispreadnlp.api.newcognitionspring.domain.response.openai.ChatCompletionChunk;
import com.myhispreadnlp.api.newcognitionspring.domain.response.openai.ChatResponse;
import com.myhispreadnlp.api.newcognitionspring.domain.response.openai.image.OpenAIImageUsage;
import io.vavr.Tuple;
import io.vavr.Tuple2;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.client.MultipartBodyBuilder;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.io.IOException;
import java.time.Instant;
import java.util.HashMap;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;

import static org.springframework.http.MediaType.MULTIPART_FORM_DATA;

@Slf4j
@Service
@RequiredArgsConstructor
@SuppressWarnings({"DuplicatedCode", "unused", "LoggingSimilarMessage"})
public class OpenaiRemoteService extends LlmRemoteService {

    @Qualifier("httpWebClient")
    private final WebClient httpWebClient;

    @Qualifier("sseWebClient")
    private final WebClient sseWebClient;

    private final ObjectMapper objectMapper;

    private final CognitionKeysCache cognitionKeysCache;

    public Flux<ServerSentEvent<String>> streamOpenAiCompletion(HashMap<String, Object> body, ChatContext chatContext) {
        var isFirst = new AtomicBoolean(true);
        return Mono.just(body)
                .doOnNext(b -> {
                    if (chatContext.getChatModelInfo().onOtherModel()) {
                        chatContext.setChannelType(ChannelType.OTHER);
                        cognitionKeysCache.setupKey(chatContext, ChannelType.OTHER);
                    } else if (chatContext.getChatModelInfo().onXModel()) {
                        chatContext.setChannelType(ChannelType.X);
                        cognitionKeysCache.setupKey(chatContext, ChannelType.X);
                    } else {
                        chatContext.setChannelType(ChannelType.OPENAI);
                        cognitionKeysCache.setupKey(chatContext, ChannelType.OPENAI);
                    }
                    log.info("开始流式请求 | 用户ID: {} | 模型: {} | 密钥: {}",
                            chatContext.getChatUserInfo().getCharactersId(), chatContext.apiModelName(), chatContext.getCurrentApiConfig().getKey());
                    b.put("model", chatContext.apiModelName());
                })
                .flatMapMany(key -> sseWebClient.post()
                        .uri(chatContext.getCurrentApiConfig().getDomain() + "/v1/chat/completions")
                        .header(HttpHeaders.AUTHORIZATION, "Bearer " + chatContext.getCurrentApiConfig().getKey())
                        .bodyValue(body)
                        .retrieve()
                        .bodyToFlux(String.class)
                        .map(processStream(chatContext, isFirst))
                        .map(response -> (ServerSentEvent.<String>builder().data(" " + response).build()))
                        .doOnComplete(() -> {
                            if (chatContext.getChatUsage().needCounterBySelf()) {
                                log.warn("触发手动计费 | 原因: stream未返回usage内容 | 用户ID: {} | 模型: {} | 密钥: {}",
                                        chatContext.getChatUserInfo().getCharactersId(), chatContext.apiModelName(),
                                        chatContext.getCurrentApiConfig().getKey());
                                chatContext.counterBySelf();
                            }
                        })
                ).doOnError(error -> {
                    loggingError(error, chatContext, ChannelType.OPENAI);
                    ModelDowngradeUtils.downgradeModel(chatContext);
                });
    }

    private static Function<String, String> processStream(ChatContext chatContext, AtomicBoolean isFirst) {
        Long userId = chatContext.getChatUserInfo().getCharactersId();
        String modelName = chatContext.apiModelName();

        return message -> {
            if (Objects.equals(message.trim(), "[DONE]")) {
                log.debug("流式请求完成 | 状态: DONE | 用户ID: {} | 模型: {}", userId, modelName);
                return message;
            }

            if (isFirst.getAndSet(false)) {
                chatContext.getChatRequestStatistic().setPreparationTime(Instant.now().toEpochMilli());
                log.debug("收到首个数据块 | 用户ID: {} | 模型: {}", userId, modelName);
            }
            try {
                var chatCompletionChunk = JsonUtils.parseObject(message, ChatCompletionChunk.class);

                // Log detailed response at DEBUG level to avoid excessive logging
                log.debug("接收数据块 | 用户ID: {} | 模型: {} | 消息内容: {}",
                        userId, modelName, message);

                chatContext.getChatRequestStatistic().getStreamResponse().append(chatCompletionChunk.allContent());

                if (StringUtils.hasLength(chatContext.getChatModelInfo().getBlackModelName())) {
                    chatCompletionChunk.setModel(chatContext.getChatModelInfo().getSystemModelName());
                }

                if (chatCompletionChunk.getUsage() != null) {
                    chatContext.setChatUsage(ChatUsage.create(chatCompletionChunk, chatContext.getChatModelInfo().needIgnoreCache()));
                    log.info("更新使用情况 | 请求类型: 流式 | 用户ID: {} | 模型: {} | 使用量: {}",
                            userId, modelName, JsonUtils.toJSONString(chatCompletionChunk.getUsage()));
                    if (chatContext.getChatModelInfo().needIgnoreCache()) {
                        log.info("忽略缓存");
                        chatCompletionChunk.getUsage().removeCache();
                    }
                }
                var newResponse = JsonUtils.toJSONString(chatCompletionChunk);
                log.debug("数据块解析完成 | 用户ID: {} | 模型: {} | 响应内容: {}",
                        userId, modelName, newResponse);
                return newResponse;
            } catch (Exception e) {
                log.error("数据块解析错误 | 用户ID: {} | 模型: {} | 错误信息: {}",
                        userId, modelName, e.getMessage());
                throw new CognitionWebException("Error occurred with OpenAI API stream parse to chatCompletionChunk: " + e.getMessage());
            }
        };
    }

    public Mono<Object> nonStreamOpenAiCompletion(HashMap<String, Object> body, ChatContext chatContext) {
        return Mono.just(body)
                .doOnNext(b -> {
                    if (chatContext.getChatModelInfo().onOtherModel()) {
                        chatContext.setChannelType(ChannelType.OTHER);
                        cognitionKeysCache.setupKey(chatContext, ChannelType.OTHER);
                    } else if (chatContext.getChatModelInfo().onXModel()) {
                        chatContext.setChannelType(ChannelType.X);
                        cognitionKeysCache.setupKey(chatContext, ChannelType.X);
                    } else {
                        chatContext.setChannelType(ChannelType.OPENAI);
                        cognitionKeysCache.setupKey(chatContext, ChannelType.OPENAI);
                    }
                    log.info("开始非流式请求 | 用户ID: {} | 模型: {} | 密钥: {}",
                            chatContext.getChatUserInfo().getCharactersId(), chatContext.apiModelName(), chatContext.getCurrentApiConfig().getKey());
                    b.put("model", chatContext.apiModelName());
                })
                .flatMap(key -> httpWebClient.post()
                        .uri(chatContext.getCurrentApiConfig().getDomain() + "/v1/chat/completions")
                        .header(HttpHeaders.AUTHORIZATION, "Bearer " + chatContext.getCurrentApiConfig().getKey())
                        .bodyValue(body)
                        .retrieve()
                        .bodyToMono(String.class)
                        .map(processNonStream(chatContext))
                ).doOnError(error -> {
                    loggingError(error, chatContext, ChannelType.OPENAI);
                    ModelDowngradeUtils.downgradeModel(chatContext);
                });
    }

    private Function<String, Object> processNonStream(ChatContext chatContext) {
        Long userId = chatContext.getChatUserInfo().getCharactersId();
        String modelName = chatContext.apiModelName();

        return message -> {
            try {
                // Log detailed response at DEBUG level to avoid excessive logging
                log.debug("接收非流式响应 | 用户ID: {} | 模型: {} | 消息内容: {}",
                        userId, modelName, message);
                var chatCompletionChunk = JsonUtils.parseObject(message, ChatCompletion.class);
                if (StringUtils.hasLength(chatContext.getChatModelInfo().getBlackModelName())) {
                    chatCompletionChunk.setModel(chatContext.getChatModelInfo().getSystemModelName());
                }

                if (chatCompletionChunk.getUsage() != null) {
                    chatContext.setChatUsage(ChatUsage.create(chatCompletionChunk, chatContext.getChatModelInfo().needIgnoreCache()));
                    log.debug("更新使用情况 | 请求类型: 非流式 | 用户ID: {} | 模型: {} | 使用量: {}",
                            userId, modelName, JsonUtils.toJSONString(chatCompletionChunk.getUsage()));
                    if (chatContext.getChatModelInfo().needIgnoreCache()) {
                        log.info("忽略缓存");
                        chatCompletionChunk.getUsage().removeCache();
                    }
                }
                return chatCompletionChunk;
            } catch (Exception e) {
                log.error("响应解析错误 | 用户ID: {} | 模型: {} | 错误信息: {}",
                        userId, modelName, e.getMessage());
                throw new CognitionWebException("Error occurred with OpenAI API stream parse to chatCompletion: " + e.getMessage());
            }
        };
    }

    public Mono<Object> nonStreamOpenAiResponse(HashMap<String, Object> body, ChatContext chatContext) {
        return Mono.just(body)
                .doOnNext(b -> {
                    chatContext.setChannelType(ChannelType.OPENAI);
                    cognitionKeysCache.setupKey(chatContext, ChannelType.OPENAI);
                    log.info("开始非流式请求 | 用户ID: {} | 模型: {} | 密钥: {}", chatContext.getChatUserInfo().getCharactersId(), chatContext.apiModelName(), chatContext.getCurrentApiConfig().getKey());
                    b.put("model", chatContext.apiModelName());
                })
                .flatMap(key -> httpWebClient.post()
                        .uri(chatContext.getCurrentApiConfig().getDomain() + "/v1/response")
                        .header(HttpHeaders.AUTHORIZATION, "Bearer " + chatContext.getCurrentApiConfig().getKey())
                        .bodyValue(body)
                        .retrieve()
                        .bodyToMono(String.class)
                        .map(processNonStreamResponse(chatContext))
                ).doOnError(error -> {
                    loggingError(error, chatContext, ChannelType.OPENAI);
                    ModelDowngradeUtils.downgradeModel(chatContext);
                });
    }

    private Function<String, Object> processNonStreamResponse(ChatContext chatContext) {
        Long userId = chatContext.getChatUserInfo().getCharactersId();
        String modelName = chatContext.apiModelName();

        return message -> {
            try {
                // Log detailed response at DEBUG level to avoid excessive logging
                log.debug("接收非流式Response响应 | 用户ID: {} | 模型: {} | 消息内容: {}",
                        userId, modelName, message);
                var usage = objectMapper.convertValue(JsonUtils.parseJson(message).path("usage"), ChatResponse.UsageDetails.class);
                if (usage != null) {
                    chatContext.setChatUsage(new ChatUsage(usage.getInputTokens(), usage.getOutputTokens(), usage.getInputTokensDetails().getCachedTokens(), usage.getOutputTokensDetails().getReasoningTokens()));
                    log.info("更新使用情况 | 请求类型: 非流式 | 用户ID: {} | 模型: {} | 使用量: {}", userId, modelName, JsonUtils.toJSONString(usage));
                }
                return message;
            } catch (Exception e) {
                log.error("响应解析错误 | 用户ID: {} | 模型: {} | 错误信息: {}", userId, modelName, e.getMessage());
                throw new CognitionWebException("Error occurred with OpenAI API stream parse to response: " + e.getMessage());
            }
        };
    }

    public Flux<ServerSentEvent<String>> streamOpenAiResponse(HashMap<String, Object> body, ChatContext chatContext) {
        var isFirst = new AtomicBoolean(true);
        return Mono.just(body)
                .doOnNext(b -> {
                    chatContext.setChannelType(ChannelType.OPENAI);
                    cognitionKeysCache.setupKey(chatContext, ChannelType.OPENAI);
                    log.info("开始流式请求 | 用户ID: {} | 模型: {} | 密钥: {}",
                            chatContext.getChatUserInfo().getCharactersId(), chatContext.apiModelName(), chatContext.getCurrentApiConfig().getKey());
                    b.put("model", chatContext.apiModelName());
                })
                .flatMapMany(key -> sseWebClient.post()
                        .uri(chatContext.getCurrentApiConfig().getDomain() + "/v1/response")
                        .header(HttpHeaders.AUTHORIZATION, "Bearer " + chatContext.getCurrentApiConfig().getKey())
                        .bodyValue(body)
                        .retrieve()
                        .bodyToFlux(String.class)
                        .map(processResponseStream(chatContext, isFirst))
                        .map(response -> (ServerSentEvent.<String>builder().data(" " + response._1).event(response._2).build()))
                ).doOnError(error -> {
                    loggingError(error, chatContext, ChannelType.OPENAI);
                    ModelDowngradeUtils.downgradeModel(chatContext);
                });
    }

    private Function<String, Tuple2<String, String>> processResponseStream(ChatContext chatContext, AtomicBoolean isFirst) {
        Long userId = chatContext.getChatUserInfo().getCharactersId();
        String modelName = chatContext.apiModelName();

        return message -> {

            if (isFirst.getAndSet(false)) {
                chatContext.getChatRequestStatistic().setPreparationTime(Instant.now().toEpochMilli());
                log.debug("收到首个数据块 | 用户ID: {} | 模型: {}", userId, modelName);
            }
            try {

                // Log detailed response at DEBUG level to avoid excessive logging
                log.debug("接收数据块 | 用户ID: {} | 模型: {} | 消息内容: {}", userId, modelName, message);

                var jsonNode = JsonUtils.parseJson(message);
                var type = jsonNode.path("type").asText();
                if ("response.completed".equalsIgnoreCase(type)) {
                    var usage = objectMapper.convertValue(jsonNode.path("response").path("usage"), ChatResponse.UsageDetails.class);
                    chatContext.setChatUsage(new ChatUsage(usage.getInputTokens(), usage.getOutputTokens(), usage.getInputTokensDetails().getCachedTokens(), usage.getOutputTokensDetails().getReasoningTokens()));
                    log.info("更新使用情况 | 请求类型: 流式 | 用户ID: {} | 模型: {} | 使用量: {}",
                            userId, modelName, JsonUtils.toJSONString(usage));
                }

                log.debug("数据块解析完成 | 用户ID: {} | 模型: {} | 响应内容: {}",
                        userId, modelName, message);
                return Tuple.of(message, type);
            } catch (Exception e) {
                log.error("数据块解析错误 | 用户ID: {} | 模型: {} | 错误信息: {}",
                        userId, modelName, e.getMessage());
                throw new CognitionWebException("Error occurred with OpenAI API stream parse to chatCompletionChunk: " + e.getMessage());
            }
        };
    }

    public Mono<Object> imageGeneration(HashMap<String, Object> body, ChatContext chatContext) {
        return Mono.just(body)
                .doOnNext(b -> {
                    chatContext.setChannelType(ChannelType.OPENAI);
                    cognitionKeysCache.setupKey(chatContext, ChannelType.OPENAI);
                    log.info("开始非流式请求 | 用户ID: {} | 模型: {} | 密钥: {}", chatContext.getChatUserInfo().getCharactersId(), chatContext.apiModelName(), chatContext.getCurrentApiConfig().getKey());
                    b.put("model", chatContext.apiModelName());
                })
                .flatMap(key -> httpWebClient.post()
                        .uri(chatContext.getCurrentApiConfig().getDomain() + "/v1/images/generations")
                        .header(HttpHeaders.AUTHORIZATION, "Bearer " + chatContext.getCurrentApiConfig().getKey())
                        .bodyValue(body)
                        .retrieve()
                        .bodyToMono(String.class)
                        .map(processImageGeneration(chatContext)))
                .doOnError(error -> loggingError(error, chatContext, ChannelType.OPENAI));
    }

    public Mono<Object> imageEdit(HashMap<String, Object> body, ChatContext chatContext) {
        return Mono.just(body)
                .doOnNext(b -> {
                    chatContext.setChannelType(ChannelType.OPENAI);
                    cognitionKeysCache.setupKey(chatContext, ChannelType.OPENAI);
                    log.info("开始非流式请求 | 用户ID: {} | 模型: {} | 密钥: {}", chatContext.getChatUserInfo().getCharactersId(), chatContext.apiModelName(), chatContext.getCurrentApiConfig().getKey());
                })
                .flatMap(b -> {
                    MultipartBodyBuilder bodyBuilder = new MultipartBodyBuilder();

                    // Add model parameter
                    bodyBuilder.part("model", b.getOrDefault("model", chatContext.apiModelName()));

                    // Add prompt parameter
                    if (b.containsKey("prompt")) {
                        bodyBuilder.part("prompt", b.get("prompt"));
                    }

                    // Add image files
                    if (b.containsKey("images")) {
                        MultipartFile[] images = (MultipartFile[]) b.get("images");
                        for (MultipartFile image : images) {
                            try {
                                bodyBuilder.part("image[]", new ByteArrayResource(image.getBytes()) {
                                    @Override
                                    public String getFilename() {
                                        return image.getOriginalFilename();
                                    }
                                });
                            } catch (IOException e) {
                                return Mono.error(new RuntimeException("Failed to process image file", e));
                            }
                        }
                    }

                    return httpWebClient.post()
                            .uri(chatContext.getCurrentApiConfig().getDomain() + "/v1/images/edits")
                            .header(HttpHeaders.AUTHORIZATION, "Bearer " + chatContext.getCurrentApiConfig().getKey())
                            .contentType(MULTIPART_FORM_DATA)
                            .body(BodyInserters.fromMultipartData(bodyBuilder.build()))
                            .retrieve()
                            .bodyToMono(String.class)
                            .map(processImageGeneration(chatContext));
                })
                .doOnError(error -> loggingError(error, chatContext, ChannelType.OPENAI));
    }

    private Function<String, Object> processImageGeneration(ChatContext chatContext) {
        return message -> {
            var usage = objectMapper.convertValue(JsonUtils.parseJson(message).path("usage"), OpenAIImageUsage.class);
            if (usage != null) {
                chatContext.setChatUsage(ChatUsage.create(usage));
                log.info("更新使用情况 | 请求类型: 非流式 | 用户ID: {} | 模型: {} | 使用量: {}", chatContext.getChatUserInfo().getCharactersId(), chatContext.apiModelName(), JsonUtils.toJSONString(usage));
            }
            try {
                return objectMapper.readValue(message, Object.class);
            } catch (JsonProcessingException e) {
                log.error("Image Generation | 数据块解析错误", e);
                throw new CognitionWebException("Error occurred with OpenAI API stream parse to Image Generation: " + e.getMessage());
            }
        };
    }
}
