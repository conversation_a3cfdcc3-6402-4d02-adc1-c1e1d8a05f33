package com.myhispreadnlp.api.newcognitionspring.channel.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum ChannelType {
    OPENA<PERSON>(false),
    AGENT(false),
    A<PERSON>UR<PERSON>(false),
    <PERSON><PERSON><PERSON><PERSON>(false),
    <PERSON><PERSON>NI(false),
    <PERSON><PERSON><PERSON><PERSON>(true),
    OTHER(false),
    X(false),
    <PERSON><PERSON><PERSON><PERSON>(false),
    <PERSON>AV<PERSON>(false),
    AWS(false);

    private final Boolean onPopKey;
}
