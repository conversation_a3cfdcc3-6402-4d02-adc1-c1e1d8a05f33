package com.myhispreadnlp.api.newcognitionspring.domain.context;

import lombok.Data;

@Data
public class ChatModelInfo {

    private Long tpm = 0L;

    private Long rpm = 0L;

    private Long rpd = 0L;

    private Long tpd = 0L;

    private Long systemModelId;

    private String rawModelName;

    private String systemModelName;

    private String blackModelName;


    public String realModelName() {
        return this.blackModelName == null ? this.systemModelName : this.blackModelName;
    }

    public void resetBlackModelName() {
        this.blackModelName = null;
    }


    public boolean needIgnoreCache() {
        return rawModelName.equalsIgnoreCase("chatgpt-4o-latest");
    }

    public boolean onOtherModel() {
        return rawModelName.equalsIgnoreCase("deepseek-r1");
    }

    public boolean onXModel() {
        return rawModelName.contains("grok");
    }

    public boolean onClaude() {
        return rawModelName.contains("claude");
    }

    public boolean onGemini() {
        return rawModelName.contains("gemini");
    }
}
