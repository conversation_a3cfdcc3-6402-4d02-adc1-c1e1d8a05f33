# Gemini OpenAI 兼容服务

本文档描述了新实现的 Google Gemini OpenAI 兼容服务的使用方法和配置。

## 概述

`GeminiOpenAIRemoteService` 是一个新的服务实现，它允许通过 OpenAI 兼容的 API 接口调用 Google Gemini 模型。该服务基于 Google AI Platform 的 OpenAI 兼容端点。

## 实现的文件

### 新增文件
1. `src/main/java/com/myhispreadnlp/api/newcognitionspring/channel/provider/openai/GeminiOpenAIRemoteService.java`
   - 主要的服务实现类
   - 支持流式和非流式请求
   - 实现 OpenAI 兼容的 chat/completions 接口

### 修改的文件
1. `src/main/java/com/myhispreadnlp/api/newcognitionspring/channel/enums/ChannelType.java`
   - 添加了 `GEMINI` 通道类型

2. `src/main/java/com/myhispreadnlp/api/newcognitionspring/domain/context/ChatModelInfo.java`
   - 添加了 `onGemini()` 方法来检测 Gemini 模型

3. `src/main/java/com/myhispreadnlp/api/newcognitionspring/cognition/openai/OpenAIFallbackOptionsFactory.java`
   - 添加了对 GeminiOpenAIRemoteService 的支持
   - 添加了 Gemini 的 fallback 选项

## API 端点

服务通过现有的 OpenAI 兼容端点暴露：

```
POST /v1/chat/completions
```

## 配置说明

### 模型名称
- 当请求中的模型名称包含 "gemini" 时，系统会自动使用 GeminiOpenAIRemoteService
- 例如：`gemini-1.5-pro`, `gemini-2.0-flash-exp` 等

### API 配置
服务需要以下配置参数：

- **domain**: Google AI Platform 的域名
  - 格式：`https://${LOCATION}-aiplatform.googleapis.com`
  - 例如：`https://us-central1-aiplatform.googleapis.com`

- **secret**: Google Cloud 项目 ID
  - 例如：`my-project-123456`

- **region**: Google Cloud 区域
  - 例如：`us-central1`

- **key**: Google Cloud 访问令牌
  - 通过 `gcloud auth print-access-token` 获取

### URI 构建
服务会自动构建正确的 Google AI Platform OpenAI 兼容端点：

```
https://${LOCATION}-aiplatform.googleapis.com/v1beta1/projects/${PROJECT_ID}/locations/${LOCATION}/endpoints/openapi/chat/completions
```

## 使用示例

### 基本请求
```bash
curl -X POST http://localhost:8080/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_AUTH_TOKEN" \
  -d '{
    "model": "gemini-1.5-pro",
    "messages": [
      {
        "role": "user",
        "content": "Hello, how are you?"
      }
    ]
  }'
```

### 流式请求
```bash
curl -X POST http://localhost:8080/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_AUTH_TOKEN" \
  -d '{
    "model": "gemini-1.5-pro",
    "messages": [
      {
        "role": "user",
        "content": "Write a story about a magic backpack."
      }
    ],
    "stream": true
  }'
```

## 特性

### 支持的功能
- ✅ 流式响应 (Server-Sent Events)
- ✅ 非流式响应
- ✅ 自动模型名称转换 (添加 "google/" 前缀)
- ✅ 使用情况统计和计费
- ✅ 错误处理和重试机制
- ✅ 日志记录和监控

### 模型名称转换
- 输入：`gemini-1.5-pro`
- 发送给 Google API：`google/gemini-1.5-pro`

### 认证
- 使用 Google Cloud 访问令牌进行认证
- 支持 Bearer token 格式
- 令牌通过 `gcloud auth print-access-token` 获取

## 错误处理

服务包含完整的错误处理机制：
- 网络错误重试
- 模型降级支持
- 详细的错误日志记录
- 自动故障转移

## 监控和日志

服务提供详细的日志记录：
- 请求开始和完成日志
- 使用情况统计
- 错误详情记录
- 性能指标追踪

## 注意事项

1. **认证令牌**: Google Cloud 访问令牌有时效性，需要定期刷新
2. **配额限制**: 遵守 Google AI Platform 的 API 配额限制
3. **区域选择**: 选择合适的区域以获得最佳性能
4. **模型可用性**: 确保所请求的 Gemini 模型在指定区域可用

## 故障排除

### 常见问题

1. **认证失败**
   - 检查访问令牌是否有效
   - 确认项目 ID 和区域配置正确

2. **模型不可用**
   - 验证模型名称是否正确
   - 检查模型在指定区域是否可用

3. **网络超时**
   - 检查网络连接
   - 验证域名和端点配置

### 调试日志
启用 DEBUG 级别日志以获取详细的请求和响应信息：

```properties
logging.level.com.myhispreadnlp.api.newcognitionspring.channel.provider.openai.GeminiOpenAIRemoteService=DEBUG
```
